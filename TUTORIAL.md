# Frame Amsterdam - Complete Development Tutorial

## 🎯 Project Overview

This project recreates the Frame Amsterdam hair salon website using modern web technologies. The design features a clean, minimalist aesthetic with cream/beige color palette, smooth animations, and responsive design.

## 🛠️ Technology Stack

### Core Framework: Next.js 14
- **App Router**: Modern routing system
- **Server Components**: Default server-side rendering
- **Image Optimization**: Built-in `next/image` component
- **Font Optimization**: Google Fonts integration
- **SEO**: Built-in metadata and OpenGraph support

### Styling: Tailwind CSS 4
- **Utility-First**: Compose styles directly in JSX
- **Custom Colors**: Extended with Frame Amsterdam brand colors
- **Responsive Design**: Mobile-first approach
- **Dark Mode**: Built-in support (not used in this project)

### Animations: Framer Motion
- **Page Transitions**: Smooth entry/exit animations
- **Scroll Animations**: Trigger animations on scroll
- **Gestures**: Hover, tap, and drag interactions
- **Layout Animations**: Automatic layout transitions

### Icons: Lucide React
- **Lightweight**: Tree-shakable SVG icons
- **Consistent**: Unified design system
- **Customizable**: Easy to style with Tailwind

### Forms: React Hook Form
- **Performance**: Minimal re-renders
- **Validation**: Built-in validation rules
- **TypeScript**: Full type safety

## 🎨 Design System

### Color Palette
```css
/* Cream Colors */
--cream-50: #FEFCF8    /* Lightest background */
--cream-100: #FDF9F1   /* Light background */
--cream-200: #FAF0E0   /* Subtle background */

/* Beige Colors */
--beige-500: #B9A587   /* Primary accent */
--beige-600: #948466   /* Buttons */
--beige-700: #6F634D   /* Hover states */
--beige-800: #4A4233   /* Text */
--beige-900: #25211A   /* Footer/dark sections */
```

### Typography
- **Font**: Inter (Google Fonts)
- **Weights**: 300 (light), 400 (regular), 500 (medium), 600 (semibold), 700 (bold)
- **Spacing**: Tailwind's built-in line-height and letter-spacing

## 📁 Project Structure

```
frame-amsterdam/
├── src/
│   ├── app/
│   │   ├── layout.tsx      # Root layout with metadata
│   │   ├── page.tsx        # Home page
│   │   └── globals.css     # Global styles
│   └── components/
│       ├── Header.tsx      # Navigation with mobile menu
│       ├── Hero.tsx        # Main hero section
│       ├── InstagramFeed.tsx # Instagram posts grid
│       ├── Newsletter.tsx  # Email signup form
│       └── Footer.tsx      # Contact info and links
├── public/
│   ├── logo.png           # Frame Amsterdam logo
│   ├── hero-image.jpg     # Main hero image
│   └── instagram-*.jpg    # Instagram post images
├── tailwind.config.ts     # Tailwind configuration
├── package.json          # Dependencies
└── TUTORIAL.md           # This file
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Git

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd frame-amsterdam

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## 💡 Key Features Implemented

### 1. Responsive Navigation
- **Desktop**: Horizontal menu with smooth animations
- **Mobile**: Hamburger menu with slide-down animation
- **Language Toggle**: Switch between NL/EN
- **Smooth Scrolling**: Anchor links to sections

### 2. Hero Section
- **Full-screen**: 100vh height with background image
- **Overlay**: Semi-transparent dark overlay for text readability
- **Animations**: Staggered text animations on load
- **CTA Button**: Call-to-action with hover effects
- **Scroll Indicator**: Animated scroll prompt

### 3. Instagram Feed
- **Grid Layout**: Responsive 1-3 column grid
- **Hover Effects**: Image scaling and overlay with stats
- **Mock Data**: Realistic Instagram post structure
- **External Links**: Links to actual Instagram posts

### 4. Newsletter Signup
- **Form Validation**: Email validation with error messages
- **Loading States**: Spinner animation during submission
- **Success State**: Confirmation message with check icon
- **Accessibility**: Proper labels and ARIA attributes

### 5. Footer
- **Multi-column**: Company info, hours, contact, social
- **Contact Info**: Phone, email, address with icons
- **Opening Hours**: Complete weekly schedule
- **Social Links**: Facebook and Instagram with icons
- **Legal Links**: Privacy, terms, cookies

## 🎨 Framer Motion Animations

### Basic Animations
```tsx
// Fade in from bottom
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.6 }}
>
  Content
</motion.div>

// Staggered animations
{items.map((item, index) => (
  <motion.div
    key={item.id}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
  >
    {item.content}
  </motion.div>
))}
```

### Scroll-triggered Animations
```tsx
<motion.div
  initial={{ opacity: 0, y: 20 }}
  whileInView={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.6 }}
  viewport={{ once: true }}
>
  Content appears when scrolled into view
</motion.div>
```

### Interactive Animations
```tsx
<motion.button
  whileHover={{ scale: 1.05 }}
  whileTap={{ scale: 0.95 }}
  transition={{ type: "spring", stiffness: 400, damping: 17 }}
>
  Interactive Button
</motion.button>
```

## 🎯 Tailwind CSS Best Practices

### Responsive Design
```tsx
// Mobile-first approach
<div className="w-full md:w-1/2 lg:w-1/3">
  Responsive width
</div>

// Responsive text
<h1 className="text-2xl md:text-4xl lg:text-6xl">
  Responsive heading
</h1>
```

### Custom Components
```tsx
// Reusable button styles
const buttonClasses = "bg-beige-600 hover:bg-beige-700 text-white font-medium py-3 px-6 transition-all duration-300"

<button className={buttonClasses}>
  Consistent styling
</button>
```

### Color Usage
```tsx
// Background colors
<div className="bg-cream-50">Light background</div>
<div className="bg-beige-800">Dark background</div>

// Text colors
<p className="text-beige-600">Primary text</p>
<p className="text-beige-400">Secondary text</p>
```

## 📱 React Hook Form Implementation

### Basic Form Setup
```tsx
const { register, handleSubmit, formState: { errors } } = useForm<FormData>()

const onSubmit = (data: FormData) => {
  // Handle form submission
}

<form onSubmit={handleSubmit(onSubmit)}>
  <input
    {...register('email', {
      required: 'Email is required',
      pattern: {
        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        message: 'Invalid email address'
      }
    })}
    type="email"
    placeholder="<EMAIL>"
  />
  {errors.email && <p>{errors.email.message}</p>}
</form>
```

## 🖼️ Next.js Image Optimization

### Basic Usage
```tsx
import Image from 'next/image'

<Image
  src="/hero-image.jpg"
  alt="Description"
  width={1920}
  height={1080}
  priority // For above-fold images
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### Responsive Images
```tsx
<Image
  src="/image.jpg"
  alt="Description"
  fill // Fill parent container
  className="object-cover"
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
/>
```

## 🔧 Customization Guide

### Adding New Sections
1. Create component in `src/components/`
2. Import and add to `src/app/page.tsx`
3. Add navigation link in `Header.tsx`

### Modifying Colors
1. Update `tailwind.config.ts` color palette
2. Update CSS variables in `globals.css`
3. Replace color classes in components

### Adding Animations
1. Import `motion` from `framer-motion`
2. Wrap elements with `motion.div`
3. Add animation properties

### Form Integration
1. Add form endpoint to environment variables
2. Update form submission logic
3. Add proper error handling

## 🚀 Deployment Options

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Configure custom domain
vercel --prod
```

### Netlify
```bash
# Build command
npm run build

# Publish directory
out/
```

### Traditional Hosting
```bash
# Export static files
npm run build
npm run export

# Upload 'out' directory to hosting
```

## 📊 Performance Optimization

### Image Optimization
- Use Next.js Image component
- Provide appropriate `sizes` attribute
- Use `priority` for above-fold images
- Implement lazy loading for below-fold content

### Code Splitting
- Use dynamic imports for large components
- Implement route-based code splitting
- Tree-shake unused dependencies

### SEO Optimization
- Implement proper metadata
- Use semantic HTML
- Add structured data
- Optimize for Core Web Vitals

## 🧪 Testing

### Setup Testing Framework
```bash
npm install --save-dev @testing-library/react @testing-library/jest-dom jest
```

### Component Testing
```tsx
import { render, screen } from '@testing-library/react'
import Header from '../components/Header'

test('renders navigation items', () => {
  render(<Header />)
  expect(screen.getByText('HOME')).toBeInTheDocument()
})
```

## 🔒 Security Best Practices

### Environment Variables
```bash
# .env.local
NEXT_PUBLIC_SITE_URL=https://frameamsterdam.nl
PRIVATE_API_KEY=your-api-key
```

### Form Security
- Validate all inputs server-side
- Implement CSRF protection
- Sanitize user inputs
- Use HTTPS in production

## 🎯 Future Enhancements

### Additional Features to Implement
1. **Booking System**: Online appointment scheduling
2. **Service Pages**: Detailed treatment descriptions
3. **Team Profiles**: Individual stylist pages
4. **Gallery**: Expanded portfolio with categories
5. **Blog**: Hair care tips and salon news
6. **Multilingual**: Full Dutch/English support
7. **Dark Mode**: Alternative color scheme
8. **PWA**: Progressive Web App features

### Performance Improvements
1. **Image CDN**: Use Cloudinary or similar
2. **Caching**: Implement Redis for dynamic content
3. **Analytics**: Add Google Analytics or alternative
4. **Monitoring**: Set up error tracking

## 📞 Support & Resources

### Documentation Links
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Framer Motion Documentation](https://www.framer.com/motion/)
- [React Hook Form Documentation](https://react-hook-form.com/)

### Community Resources
- [Next.js Discord](https://discord.gg/nextjs)
- [Tailwind CSS Discord](https://discord.gg/tailwindcss)
- [React Community](https://reactjs.org/community/support.html)

---

## 🎉 Congratulations!

You now have a complete understanding of the Frame Amsterdam website implementation. The project demonstrates modern web development practices with a focus on performance, accessibility, and user experience.

Feel free to customize and extend the project according to your specific needs. The modular architecture makes it easy to add new features and modify existing ones. 