'use client';

import { useEffect } from 'react';
import Cookies from 'js-cookie';

// Facebook Pixel ID from environment variables
const FACEBOOK_PIXEL_ID = process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID || '1077679850383928';

// Set cookie duration to 90 days as recommended by Facebook
const COOKIE_DURATION_DAYS = 90;

// Generate unique event ID for deduplication
const generateEventId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Get client information for CAPI
const getClientInfo = () => {
  if (typeof window === 'undefined') return {};

  return {
    user_agent: navigator.userAgent,
    source_url: window.location.href,
    fbp: Cookies.get('_fbp'),
    fbc: Cookies.get('_fbc')
  };
};

// Send event to server-side CAPI
const sendServerEvent = async (eventName: string, eventData: Record<string, unknown> = {}) => {
  try {
    const eventId = generateEventId();
    const clientInfo = getClientInfo();

    await fetch('/api/facebook-events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        event_name: eventName,
        event_id: eventId,
        event_time: Math.floor(Date.now() / 1000),
        action_source: 'website',
        event_source_url: clientInfo.source_url,
        user_data: {
          client_ip_address: '', // Will be filled server-side
          client_user_agent: clientInfo.user_agent,
          fbp: clientInfo.fbp,
          fbc: clientInfo.fbc,
        },
        custom_data: eventData,
      }),
    });
  } catch (error) {
    console.error('Failed to send server event:', error);
  }
};

// Initialize Facebook Pixel if consent is granted
export const initFacebookPixel = () => {
  if (typeof window !== 'undefined') {
    // Check if fbq is already initialized
    if (!window.fbq) {
      // Define the fbq function with proper queue initialization
      window.fbq = function(...args: unknown[]) {
        if (window.fbq.callMethod) {
          window.fbq.callMethod(...args);
        } else {
          if (!window.fbq.queue) {
            window.fbq.queue = [];
          }
          window.fbq.queue.push(args);
        }
      };

      // Set up Facebook Pixel global variables
      if (!window._fbq) {
        window._fbq = window.fbq;
      }

      // Set up Facebook Pixel properties
      window.fbq.push = window.fbq;
      window.fbq.loaded = true;
      window.fbq.version = '2.0';
      window.fbq.queue = [];
    }

    // Load the pixel script
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://connect.facebook.net/en_US/fbevents.js';
    document.head.appendChild(script);

    // Initialize with the pixel ID
    window.fbq('init', FACEBOOK_PIXEL_ID);

    // Default to requiring consent (revoked state)
    window.fbq('consent', 'revoke');

    // If consent was previously given, grant it
    const hasConsent = Cookies.get('fb_pixel_consent') === 'true';
    if (hasConsent) {
      window.fbq('consent', 'grant');
      trackPageView();
    }
  }
};

// Track PageView event (hybrid: client + server)
export const trackPageView = () => {
  if (typeof window !== 'undefined' && Cookies.get('fb_pixel_consent') === 'true') {
    const eventId = generateEventId();

    // Client-side tracking
    if (window.fbq) {
      window.fbq('track', 'PageView', {}, { eventID: eventId });
    }

    // Server-side tracking
    sendServerEvent('PageView', {});
  }
};

// Track InitiateCheckout event (hybrid: client + server)
export const trackInitiateCheckout = (value?: number, currency: string = 'EUR') => {
  if (typeof window !== 'undefined' && Cookies.get('fb_pixel_consent') === 'true') {
    const eventId = generateEventId();
    const eventData = {
      content_type: 'service',
      content_name: 'Booking Widget',
      ...(value && { value, currency })
    };

    // Client-side tracking
    if (window.fbq) {
      window.fbq('track', 'InitiateCheckout', eventData, { eventID: eventId });
    }

    // Server-side tracking
    sendServerEvent('InitiateCheckout', eventData);
  }
};

// Function to call when consent is granted
export const grantFacebookPixelConsent = () => {
  if (typeof window !== 'undefined') {
    // Initialize Facebook Pixel if not already initialized
    initFacebookPixel();

    // Set consent cookie with proper domain and security
    Cookies.set('fb_pixel_consent', 'true', {
      expires: COOKIE_DURATION_DAYS,
      secure: window.location.protocol === 'https:',
      sameSite: 'lax'
    });

    if (window.fbq) {
      // Grant consent to Facebook Pixel
      window.fbq('consent', 'grant');

      // Track initial page view
      trackPageView();
    }
  }
};

// Function to call when consent is revoked
export const revokeFacebookPixelConsent = () => {
  if (typeof window !== 'undefined') {
    // Update consent cookie to false
    Cookies.set('fb_pixel_consent', 'false', {
      expires: COOKIE_DURATION_DAYS,
      secure: window.location.protocol === 'https:',
      sameSite: 'lax'
    });

    if (window.fbq) {
      // Revoke consent from Facebook Pixel
      window.fbq('consent', 'revoke');
    }

    // Remove Facebook Pixel scripts and cookies for complete opt-out
    try {
      // Remove FB script tags
      const scripts = document.querySelectorAll('script[src*="facebook"]');
      scripts.forEach(script => script.parentNode?.removeChild(script));

      // Remove FB cookies with proper domain handling
      const fbCookies = ['_fbp', '_fbc'];
      fbCookies.forEach(name => {
        Cookies.remove(name);
        Cookies.remove(name, { path: '/' });
        // For top-level domains
        const domain = window.location.hostname;
        if (domain.includes('.')) {
          const topDomain = domain.substring(domain.indexOf('.'));
          Cookies.remove(name, { path: '/', domain: topDomain });
        }
      });
    } catch (error) {
      console.error('Error removing Facebook tracking:', error);
    }
  }
};

// Facebook Pixel component to initialize tracking
export default function FacebookPixel() {
  useEffect(() => {
    // Initialize the Facebook Pixel
    initFacebookPixel();

    // Track page views on route changes for Next.js App Router
    const handleRouteChange = () => {
      // Small delay to ensure the new page is loaded
      setTimeout(() => {
        trackPageView();
      }, 100);
    };

    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', handleRouteChange);

    // Listen for Next.js route changes (if available)
    document.addEventListener('nextjs:route-change-complete', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
      document.removeEventListener('nextjs:route-change-complete', handleRouteChange);
    };
  }, []);

  return null;
}

// Add TypeScript declaration for fbq
declare global {
  interface Window {
    fbq: {
      (...args: unknown[]): void;
      callMethod?: (...args: unknown[]) => void;
      queue?: unknown[];
      push?: unknown;
      loaded?: boolean;
      version?: string;
    };
    _fbq: unknown;
  }
}


