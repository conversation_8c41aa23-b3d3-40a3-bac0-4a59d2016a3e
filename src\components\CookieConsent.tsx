'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Cookies from 'js-cookie';
import { grantFacebookPixelConsent } from './FacebookPixel';
import { Variants, motion } from 'framer-motion';

const COOKIE_NAME = 'cookie_consent_v2'
const COOKIE_DURATION_DAYS = 180
type ConsentCategories = 'essential' | 'analytics' | 'marketing'
type ConsentState = Record<ConsentCategories, boolean>


function getStoredConsent(): ConsentState | null {
  try {
    const raw = Cookies.get(COOKIE_NAME)
    if (!raw) return null
    return JSON.parse(raw) as ConsentState
  } catch {
    return null
  }
}

function setStoredConsent(consent: ConsentState): void {
  Cookies.set(COOKIE_NAME, JSON.stringify(consent), { expires: COOKIE_DURATION_DAYS })
}

function clearConsentCookies(): void {
  Cookies.remove(COOKIE_NAME)
}

// Global function to open cookie settings from anywhere
export function openCookieSettings(): void {
  // Dispatch a custom event that the CookieConsent component will listen to
  window.dispatchEvent(new CustomEvent('openCookieSettings'))
}

export default function CookieConsent(): React.ReactNode {
  const [visible, setVisible] = useState(false)
  const [settingsOpen, setSettingsOpen] = useState(false)
  const [consent, setConsent] = useState<ConsentState>(() => getStoredConsent() || {
    essential: true,
    analytics: false,
    marketing: false
  })
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    const stored = getStoredConsent()
    if (!stored) {
      const timer = setTimeout(() => setVisible(true), 1000)
      return () => clearTimeout(timer)
    } else {
      setConsent(stored)
      setInitialized(true)
    }
  }, [])

  useEffect(() => {
    if (!initialized) return
    // Grant/revoke Facebook Pixel based on marketing consent
    if (consent.marketing) {
      grantFacebookPixelConsent()
    } else {
      // If marketing consent is withdrawn, use our cookie to track this
      // Facebook Pixel will check this cookie on its own
      Cookies.set('fb_pixel_consent', 'false', { expires: COOKIE_DURATION_DAYS })
    }
  }, [consent, initialized])

  function handleAcceptAll(): void {
    const newConsent: ConsentState = {
      essential: true,
      analytics: true,
      marketing: true
    }
    setConsent(newConsent)
    setStoredConsent(newConsent)
    setVisible(false)
    setSettingsOpen(false)
    setInitialized(true)
  }

  function handleRejectAll(): void {
    const newConsent: ConsentState = {
      essential: true,
      analytics: false,
      marketing: false
    }
    setConsent(newConsent)
    setStoredConsent(newConsent)
    setVisible(false)
    setSettingsOpen(false)
    setInitialized(true)
  }

  function handleSaveCustom(): void {
    setStoredConsent(consent)
    setVisible(false)
    setSettingsOpen(false)
    setInitialized(true)
  }

  function handleOpenSettings(): void {
    setSettingsOpen(true)
    setVisible(true)
  }

  function handleWithdrawConsent(): void {
    clearConsentCookies()
    setConsent({
      essential: true,
      analytics: false,
      marketing: false
    })
    setInitialized(false)
    setVisible(true)
    setSettingsOpen(true)
  }

  function handleCategoryChange(cat: ConsentCategories): void {
    if (cat === 'essential') return // always true
    setConsent((prev: ConsentState) => ({ ...prev, [cat]: !prev[cat] }))
  }

  // Modal animation
  const modalVariants: Variants = {
    hidden: { opacity: 0, y: 20, scale: 0.9 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { type: 'spring', damping: 25, stiffness: 500, duration: 0.4 } },
    exit: { opacity: 0, y: -20, scale: 0.9, transition: { duration: 0.2 } }
  }

  // Listen for external requests to open cookie settings
  useEffect(() => {
    const handleOpenCookieSettings = () => {
      handleOpenSettings()
    }
    
    window.addEventListener('openCookieSettings', handleOpenCookieSettings)
    
    return () => {
      window.removeEventListener('openCookieSettings', handleOpenCookieSettings)
    }
  }, [])

  // Show persistent "Cookie Settings" button
  // useEffect(() => {
  //   if (typeof window === 'undefined') return
  //   let btn = document.getElementById('cookie-settings-btn')
  //   if (!btn) {
  //     btn = document.createElement('button')
  //     btn.id = 'cookie-settings-btn'
  //     btn.innerText = '⚙️'
  //     btn.className = 'fixed bottom-4 left-4 z-40 bg-black text-white w-10 h-10 rounded-full shadow-lg hover:bg-gray-800 transition-colors flex items-center justify-center'
  //     btn.style.fontSize = '16px'
  //     btn.onclick = handleOpenSettings
  //     document.body.appendChild(btn)
  //   } else {
  //     btn.onclick = handleOpenSettings
  //   }
  //   return () => {
  //     if (btn) {
  //       btn.removeEventListener('click', handleOpenSettings)
  //     }
  //   }
  // }, [])

  if (!visible) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50" role="dialog" aria-modal="true">
      <motion.div 
        className="max-w-md w-full bg-white shadow-xl rounded-xl relative overflow-hidden"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={modalVariants}
        style={{ backgroundColor: '#fff' }}
      >
        <div className="p-8">
          <div className="flex justify-center mb-8">
            <Image 
              src="/Logo/logo.png" 
              alt="Studio Joshi Logo" 
              width={160}
              height={120}
              className="h-28 object-contain"
              style={{ maxHeight: '120px' }}
            />
          </div>
          <div className="text-gray-700 text-sm sm:text-base mb-4">
            <p className="font-medium mb-1">We gebruiken cookies voor een betere ervaring</p>
            <p className="text-gray-500 text-xs sm:text-sm">
              Kies welke cookies je wilt toestaan. Je kunt je voorkeuren altijd aanpassen via de Cookie instellingen.
            </p>
          </div>
          {settingsOpen ? (
            <div className="space-y-3 mb-6">
              <ConsentToggle
                label="Essentiële cookies"
                checked={true}
                disabled={true}
                onChange={() => {}}
              />
              <ConsentToggle
                label="Analytics cookies"
                checked={consent.analytics}
                onChange={() => handleCategoryChange('analytics')}
                disabled={false}
              />
              <ConsentToggle
                label="Marketing cookies (Facebook Pixel)"
                checked={consent.marketing}
                onChange={() => handleCategoryChange('marketing')}
                disabled={false}
              />
            </div>
          ) : null}
          <div className="flex items-center gap-4 w-full mt-6">
            {settingsOpen ? (
              <>
                <button
                  onClick={handleRejectAll}
                  className="text-gray-500 text-sm font-medium hover:text-gray-700 transition-colors"
                  aria-label="Weiger alle cookies"
                >
                  Weiger alles
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="bg-black text-white font-medium px-4 py-2 rounded-full hover:bg-gray-800 transition-colors"
                  aria-label="Accepteer alles"
                >
                  Accepteer alles
                </button>
                <button
                  onClick={handleSaveCustom}
                  className="bg-gray-200 text-black font-medium px-4 py-2 rounded-full hover:bg-gray-300 transition-colors"
                  aria-label="Sla voorkeuren op"
                >
                  Sla voorkeuren op
                </button>
                <button
                  onClick={handleWithdrawConsent}
                  className="text-xs text-red-500 ml-auto underline"
                  aria-label="Trek toestemming in"
                >
                  Trek toestemming in
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setSettingsOpen(true)}
                  className="text-gray-500 text-sm font-medium hover:text-gray-700 transition-colors"
                  aria-label="Cookie instellingen aanpassen"
                >
                  Instellingen
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="bg-black text-white font-medium px-8 py-3 rounded-full hover:bg-gray-800 transition-colors flex-1"
                  aria-label="Accepteer alles"
                >
                  Accepteer alles
                </button>
              </>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  )
}

interface ConsentToggleProps {
  label: string
  checked: boolean
  onChange: () => void
  disabled: boolean
}

function ConsentToggle({ label, checked, onChange, disabled }: ConsentToggleProps): React.ReactNode {
  return (
    <label className="flex items-center gap-2 cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        className="form-checkbox h-5 w-5 text-black"
        aria-checked={checked}
      />
      <span className={disabled ? 'text-gray-400' : 'text-gray-700'}>{label}</span>
    </label>
  )
}

