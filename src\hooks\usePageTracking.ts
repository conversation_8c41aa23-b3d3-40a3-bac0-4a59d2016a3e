'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { trackPageView } from '@/components/FacebookPixel';

/**
 * Custom hook for tracking page views in Next.js App Router
 * Automatically tracks page views when the pathname changes
 */
export function usePageTracking() {
  const pathname = usePathname();

  useEffect(() => {
    // Track page view when pathname changes
    trackPageView();
  }, [pathname]);
}

export default usePageTracking;
