/**
 * Facebook Pixel Testing Script
 * Run this in browser console to test Facebook Pixel implementation
 */

console.log('🔍 Facebook Pixel Testing Script');
console.log('================================');

// Test 1: Check if Facebook Pixel is loaded
console.log('\n1. Checking Facebook Pixel...');
if (typeof window.fbq !== 'undefined') {
  console.log('✅ Facebook Pixel is loaded');
  console.log('   Version:', window.fbq.version || 'Unknown');
  console.log('   Queue length:', window.fbq.queue?.length || 0);
} else {
  console.log('❌ Facebook Pixel is NOT loaded');
}

// Test 2: Check cookie consent status
console.log('\n2. Checking Cookie Consent...');
const consentCookie = document.cookie
  .split('; ')
  .find(row => row.startsWith('fb_pixel_consent='));

if (consentCookie) {
  const consentValue = consentCookie.split('=')[1];
  console.log(`✅ Consent cookie found: ${consentValue}`);
  
  if (consentValue === 'true') {
    console.log('✅ Facebook tracking is ENABLED');
  } else {
    console.log('⚠️ Facebook tracking is DISABLED');
  }
} else {
  console.log('❌ No consent cookie found');
}

// Test 3: Check Facebook cookies
console.log('\n3. Checking Facebook Cookies...');
const fbpCookie = document.cookie
  .split('; ')
  .find(row => row.startsWith('_fbp='));
const fbcCookie = document.cookie
  .split('; ')
  .find(row => row.startsWith('_fbc='));

if (fbpCookie) {
  console.log('✅ _fbp cookie found:', fbpCookie.split('=')[1]);
} else {
  console.log('⚠️ _fbp cookie not found');
}

if (fbcCookie) {
  console.log('✅ _fbc cookie found:', fbcCookie.split('=')[1]);
} else {
  console.log('⚠️ _fbc cookie not found (normal if no Facebook click)');
}

// Test 4: Test API endpoint
console.log('\n4. Testing CAPI Endpoint...');
fetch('/api/facebook-events', {
  method: 'GET'
})
.then(response => response.json())
.then(data => {
  console.log('✅ CAPI endpoint is responding:', data);
})
.catch(error => {
  console.log('❌ CAPI endpoint error:', error);
});

// Test 5: Manual event tracking test
console.log('\n5. Manual Event Test...');
if (typeof window.fbq !== 'undefined' && consentCookie?.split('=')[1] === 'true') {
  console.log('🧪 Sending test PageView event...');
  window.fbq('track', 'PageView');
  console.log('✅ Test event sent (check Network tab for requests)');
} else {
  console.log('⚠️ Cannot send test event (pixel not loaded or consent not given)');
}

// Test 6: Network requests monitoring
console.log('\n6. Network Monitoring Instructions:');
console.log('   1. Open DevTools → Network tab');
console.log('   2. Filter by "facebook" or "graph.facebook.com"');
console.log('   3. Perform actions on the website');
console.log('   4. Look for:');
console.log('      - facebook.com/tr requests (client-side)');
console.log('      - /api/facebook-events requests (server-side)');
console.log('      - graph.facebook.com requests (CAPI)');

// Test 7: Event deduplication check
console.log('\n7. Event Deduplication Test:');
console.log('   Events should have unique event_id parameters');
console.log('   Check Facebook Events Manager for duplicate detection');

console.log('\n🎯 Testing Complete!');
console.log('📊 Check Facebook Events Manager for real-time events');
console.log('🔗 Events Manager: https://business.facebook.com/events_manager2/');

// Helper function to grant consent for testing
window.testGrantConsent = function() {
  console.log('🧪 Granting consent for testing...');
  if (typeof grantFacebookPixelConsent !== 'undefined') {
    grantFacebookPixelConsent();
    console.log('✅ Consent granted - reload page to see effects');
  } else {
    console.log('❌ grantFacebookPixelConsent function not available');
  }
};

// Helper function to revoke consent for testing
window.testRevokeConsent = function() {
  console.log('🧪 Revoking consent for testing...');
  if (typeof revokeFacebookPixelConsent !== 'undefined') {
    revokeFacebookPixelConsent();
    console.log('✅ Consent revoked - reload page to see effects');
  } else {
    console.log('❌ revokeFacebookPixelConsent function not available');
  }
};

console.log('\n🛠️ Helper Functions Available:');
console.log('   testGrantConsent() - Grant consent for testing');
console.log('   testRevokeConsent() - Revoke consent for testing');
