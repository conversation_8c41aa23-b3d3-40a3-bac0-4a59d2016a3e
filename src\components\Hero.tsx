'use client'

import { motion, useScroll, useTransform } from 'framer-motion'

import Image from 'next/image'
import { useRef } from 'react'
import BookingWidget from './BookingWidget'
import { useLanguage } from '@/contexts/LanguageContext'

export default function Hero() {
  const containerRef = useRef(null)
  const { t } = useLanguage()
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  })

  // Only parallax for main content, not decorative elements
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "25%"])

  return (
    <section 
      id="home"
      ref={containerRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-off-white px-4 sm:px-6 lg:px-8 pt-24 sm:pt-28 md:pt-32 lg:pt-28"
    >
      {/* Content with Parallax */}
      <motion.div 
        className="relative z-10 w-full max-w-4xl mx-auto text-center"
        style={{ y: textY }}
      >
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="space-y-6 sm:space-y-8"
        >
          {/* Main Title with Staggered Animation */}
          <motion.h1
            className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-title font-regular text-dark-brown leading-tight px-2"
            initial={{ opacity: 0, y: 80 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.2, delay: 0.2, ease: "easeOut" }}
          >
            {t('hero.welcome').trim() && t('hero.welcome') !== 'hero.welcome' && (
              <>
                <motion.span
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  {t('hero.welcome')}
                </motion.span>{' '}
              </>
            )}
            <motion.div 
              className="block"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Image
                src="/Logo/Laag_1.png"
                alt="Studio Joshi"
                width={400}
                height={200}
                className="w-auto h-16 sm:h-20 md:h-24 lg:h-28 mx-auto"
                priority
              />
            </motion.div>
          </motion.h1>

          {/* Subtitle with Fade In */}
          <motion.p
            className="text-lg sm:text-xl md:text-2xl font-subtitle font-light text-light-brown max-w-2xl mx-auto leading-relaxed px-4"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            {t('hero.subtitle')}
          </motion.p>

          {/* Description with Slide Up */}
          <motion.p
            className="text-sm sm:text-base md:text-lg font-body font-light text-dark-brown/80 max-w-3xl mx-auto leading-relaxed px-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
          >
            {t('hero.description')}
          </motion.p>

          {/* CTA Buttons with Booking Integration */}
          <motion.div
            className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center pt-6 sm:pt-8 px-4"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ 
              duration: 0.8, 
              delay: 1.2,
              type: "spring",
              stiffness: 100
            }}
          >
            {/* Primary CTA - Booking */}
            <BookingWidget variant="hero" />

            <motion.a
              href="#about-title"
              className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 border-2 border-warm-beige text-dark-brown font-subtitle font-regular text-sm tracking-wide rounded-full hover:bg-warm-beige hover:text-off-white transition-all duration-300"
              whileHover={{ 
                scale: 1.05, 
                y: -5,
                boxShadow: "0 15px 30px rgba(178, 152, 124, 0.3)"
              }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 1.6 }}
            >
              {t('hero.cta')}
            </motion.a>
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  )
} 