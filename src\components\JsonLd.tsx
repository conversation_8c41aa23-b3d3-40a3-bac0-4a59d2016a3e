'use client'

interface JsonLdProps {
  hairdresserData: {
    name: string
    description: string
    url: string
    telephone: string
    address: {
      streetAddress: string
      addressLocality: string
      postalCode: string
      addressCountry: string
    }
    geo: {
      latitude: number
      longitude: number
    }
    openingHours: string[]
    image: string
  }
  servicesOffered: {
    name: string
    description: string
    url: string
  }[]
}

export default function JsonLd({ hairdresserData, servicesOffered }: JsonLdProps) {
  const hairdresserSchema = {
    '@context': 'https://schema.org',
    '@type': 'HairSalon',
    '@id': hairdresserData.url,
    name: hairdresserData.name,
    description: hairdresserData.description,
    url: hairdresserData.url,
    telephone: hairdresserData.telephone,
    address: {
      '@type': 'PostalAddress',
      streetAddress: hairdresserData.address.streetAddress,
      addressLocality: hairdresserData.address.addressLocality,
      postalCode: hairdresserData.address.postalCode,
      addressCountry: hairdresserData.address.addressCountry,
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: hairdresserData.geo.latitude,
      longitude: hairdresserData.geo.longitude,
    },
    image: hairdresserData.image,
    openingHoursSpecification: hairdresserData.openingHours.map(hours => ({
      '@type': 'OpeningHoursSpecification',
      dayOfWeek: hours.split(' ')[0],
      opens: hours.split(' ')[1].split('-')[0],
      closes: hours.split(' ')[1].split('-')[1]
    })),
    priceRange: '€€',
    makesOffer: servicesOffered.map(service => ({
      '@type': 'Offer',
      itemOffered: {
        '@type': 'Service',
        name: service.name,
        description: service.description,
        url: service.url
      }
    }))
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(hairdresserSchema)
      }}
    />
  )
}
