'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion, AnimatePresence, useScroll, useMotionValueEvent } from 'framer-motion'
import { Menu, X, Globe } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const { scrollY } = useScroll()
  const { language, setLanguage, t } = useLanguage()

  useMotionValueEvent(scrollY, "change", (latest) => {
    setIsScrolled(latest > 100)
  })

  const navigation = [
    { name: t('nav.home'), href: '#home' },
    { name: t('nav.about'), href: '#over-ons' },
    { name: t('nav.services'), href: '#diensten' },
    { name: t('nav.hours'), href: '#openingstijden' },
    { name: t('nav.contact'), href: '#contact' },
  ]



  return (
    <motion.header
      className="fixed top-0 left-0 right-0 z-50 transition-all duration-300"
      animate={{
        backdropFilter: isScrolled ? 'blur(20px)' : 'blur(8px)',
        backgroundColor: isScrolled 
          ? 'rgba(237, 230, 217, 0.98)'
          : 'rgba(237, 230, 217, 0.85)',
      }}
      style={{
        borderBottom: isScrolled ? '1px solid rgba(119, 90, 65, 0.3)' : '1px solid transparent',
        boxShadow: isScrolled ? '0 4px 20px rgba(119, 90, 65, 0.1)' : 'none',
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Desktop Layout */}
        <div className="hidden md:block py-4">
          {/* Logo - Centered at top - Smaller size */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-center mb-4"
          >
            <Link href="/" className="flex items-center">
              <Image
                src="/Logo/logo.png"
                alt="Studio Joshi"
                width={200}
                height={200}
                className="w-auto h-16 lg:h-20"
                priority
              />
            </Link>
          </motion.div>

          {/* Navigation Menu - Centered below logo */}
          <div className="flex items-center justify-center space-x-6">
            <nav className="flex items-center space-x-6">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className={`text-xs lg:text-sm font-medium tracking-wide transition-colors duration-200 ${
                      isScrolled 
                        ? 'text-dark-brown hover:text-light-brown' 
                        : 'text-dark-brown hover:text-light-brown'
                    }`}
                  >
                    {item.name}
                  </Link>
                </motion.div>
              ))}
            </nav>

            {/* Language Selector */}
            <div className="flex items-center space-x-1 ml-6">
              <button
                onClick={() => setLanguage(language === 'NL' ? 'EN' : 'NL')}
                className={`flex items-center space-x-1 text-xs lg:text-sm font-medium transition-colors duration-200 ${
                  isScrolled 
                    ? 'text-dark-brown hover:text-light-brown'
                    : 'text-dark-brown hover:text-light-brown'
                }`}
              >
                <Globe className="w-3 h-3 lg:w-4 lg:h-4" />
                <span>{language}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden relative flex items-center justify-center py-6">
          {/* Mobile Logo - Centered and Smaller */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-center"
          >
            <Link href="/" className="flex items-center">
              <Image
                src="/Logo/logo.png"
                alt="Studio Joshi"
                width={120}
                height={120}
                className="w-auto h-20 sm:h-24"
                priority
              />
            </Link>
          </motion.div>

          {/* Mobile menu button - Absolute positioned to right */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`absolute right-0 text-beige-800 hover:text-beige-600 transition-colors duration-200 ${
              isScrolled 
                ? 'text-dark-brown hover:text-light-brown'
                : 'text-dark-brown hover:text-light-brown'
            }`}
          >
            {isMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            {/* Background Overlay */}
            <motion.div
              className="fixed inset-0 bg-dark-brown/20 backdrop-blur-sm z-40 md:hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              onClick={() => setIsMenuOpen(false)}
            />
            
            {/* Menu Panel */}
          <motion.div
              className="absolute top-full left-0 w-full bg-off-white/95 backdrop-blur-xl shadow-2xl border-t border-warm-beige/30 z-50 md:hidden"
              initial={{ 
                opacity: 0, 
                y: -20,
                scale: 0.95
              }}
            animate={{ 
                opacity: 1, 
                y: 0,
                scale: 1
              }}
              exit={{ 
                opacity: 0, 
                y: -20,
                scale: 0.95
            }}
              transition={{ 
                duration: 0.4, 
                ease: [0.23, 1, 0.32, 1] // Custom easing for smooth feel
              }}
            >
              {/* Enhanced background with gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-b from-off-white/98 to-warm-beige/95 backdrop-blur-xl"></div>
              
              <div className="relative px-6 py-8 space-y-1">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                    initial={{ 
                      opacity: 0, 
                      x: -30,
                      scale: 0.9
                    }}
                  animate={{ 
                      opacity: 1, 
                      x: 0,
                      scale: 1
                    }}
                    exit={{ 
                      opacity: 0, 
                      x: -30,
                      scale: 0.9
                  }}
                  transition={{ 
                      duration: 0.4, 
                      delay: index * 0.08,
                      ease: [0.23, 1, 0.32, 1]
                  }}
                >
                  <Link
                    href={item.href}
                      className="block w-full text-left py-4 px-4 text-xl font-subtitle font-regular text-dark-brown hover:text-light-brown hover:bg-warm-beige/20 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-95"
                    onClick={() => setIsMenuOpen(false)}
                    >
                      <motion.span
                        whileHover={{ x: 8 }}
                        transition={{ duration: 0.2 }}
                  >
                    {item.name}
                      </motion.span>
                  </Link>
                </motion.div>
              ))}
                
                {/* Language Selector in Mobile Menu */}
                <motion.div
                  className="pt-4 mt-4 border-t border-warm-beige/30"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ 
                    duration: 0.4, 
                    delay: navigation.length * 0.08 + 0.1,
                    ease: [0.23, 1, 0.32, 1]
                  }}
                >
                  <button
                    onClick={() => setLanguage(language === 'NL' ? 'EN' : 'NL')}
                    className="flex items-center space-x-2 py-3 px-4 text-lg font-subtitle font-regular text-light-brown hover:text-dark-brown transition-colors duration-200"
                  >
                    <Globe className="w-5 h-5" />
                    <span>{t('language.label')}{language}</span>
                  </button>
                </motion.div>
            </div>
          </motion.div>
          </>
        )}
      </AnimatePresence>
    </motion.header>
  )
}

export default Header 