'use client';

import dynamic from 'next/dynamic';

// Dynamically import client components 
const FacebookPixel = dynamic(() => import('./FacebookPixel'), { ssr: false });
const CookieConsent = dynamic(() => import('./CookieConsent'), { ssr: false });

// Client component wrapper for cookie consent and tracking
export default function ClientComponents() {
  return (
    <>
      <FacebookPixel />
      <CookieConsent />
    </>
  );
}
