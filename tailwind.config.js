/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Studio Joshi Brand Colors
        'dark-brown': '#392414',
        'light-brown': '#775A41', 
        'warm-beige': '#B2987C',
        'off-white': '#EDE6D9',
        
        // Extended palette for variations
        brown: {
          50: '#faf9f7',
          100: '#f5f2ed',
          200: '#ebe4db',
          300: '#ddd1c1',
          400: '#ccb59f',
          500: '#B2987C', // warm-beige
          600: '#9d8369',
          700: '#775A41', // light-brown
          800: '#5d4532',
          900: '#392414', // dark-brown
        },
        beige: {
          50: '#fdfcfa',
          100: '#faf8f4',
          200: '#f4f0e9',
          300: '#EDE6D9', // off-white
          400: '#e3d8c7',
          500: '#d6c8b2',
          600: '#c7b49a',
          700: '#B2987C', // warm-beige
          800: '#9d8369',
          900: '#8a7159',
        }
      },
      fontFamily: {
        // Typography from brand guidelines
        'title': ['NewYork', 'Times New Roman', 'serif'], // For titles
        'subtitle': ['Ranade', 'Inter', 'sans-serif'], // For subtitles  
        'body': ['Ranade', 'Inter', 'sans-serif'], // For body text
        'sans': ['Ranade', 'Inter', 'system-ui', 'sans-serif'],
        'serif': ['NewYork', 'Times New Roman', 'serif'],
      },
      fontWeight: {
        'light': '300',
        'regular': '400',
        'medium': '500',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
} 