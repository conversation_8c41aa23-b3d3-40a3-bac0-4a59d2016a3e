import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from '@/contexts/LanguageContext';
import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from "@vercel/speed-insights/next"
import SeoStructuredData from "@/components/SeoStructuredData";
import ClientComponents from "@/components/ClientComponents";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

// Define metadata for SEO and social sharing
export const metadata: Metadata = {
  title: "Studio Joshi - Hairextensions & Balayage Specialist | Highlights & Haarverlenging",
  description: "Professionele hairextensions, balayage en haarverlenging in Hazerswoude. Specialist in weft extensions en styling met meer dan 10 jaar ervaring.",
  keywords: ["hairextensions", "balayage", "haar extensions", "haarverlenging", "weave extensions", "weft extensions", "hairextensions prijs", "extensions verwijderen", "Studio Joshi"],
  authors: [{ name: "Studio Joshi" }],
  metadataBase: new URL("https://studiojoshi.nl"),
  openGraph: {
    title: "Studio Joshi - Hairextensions & Balayage Specialist | Highlights & Haarverlenging",
    description: "Professionele hairextensions, balayage en haarverlenging in Hazerswoude. Specialist in weft extensions en styling.",
    url: "https://studiojoshi.nl",
    siteName: "Studio Joshi",
    locale: "nl_NL",
    type: "website",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Studio Joshi - Hairextensions & Balayage Specialist in Hazerswoude",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Studio Joshi - Hairextensions & Balayage Specialist | Highlights",
    description: "Professionele hairextensions, balayage en haarverlenging in Hazerswoude. Specialist in weft extensions en styling.",
    images: ["/og-image.png"],
  },
  robots: "index, follow",
};

// Separate viewport configuration as required by Next.js 15+
export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

// Facebook Pixel and Cookie Consent are included via ClientComponents

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="nl" className="scroll-smooth" suppressHydrationWarning>
      <head>
        <SeoStructuredData />
      </head>
      <body className={`${inter.variable} font-sans antialiased`} suppressHydrationWarning>
        <LanguageProvider>
          {children}
        </LanguageProvider>
        <Analytics />
        <SpeedInsights />
        <ClientComponents />
      </body>
    </html>
  );
}
