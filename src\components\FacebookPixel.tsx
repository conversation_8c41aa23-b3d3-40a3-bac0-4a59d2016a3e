'use client';

import { useEffect } from 'react';
import Cookies from 'js-cookie';

// Facebook Pixel ID (replace with your actual pixel ID)
const FACEBOOK_PIXEL_ID = 'YOUR_PIXEL_ID_HERE';

// Set cookie duration to 180 days for consistency with consent system
const COOKIE_DURATION_DAYS = 180;

// Initialize Facebook Pixel if consent is granted
export const initFacebookPixel = () => {
  if (typeof window !== 'undefined') {
    // Check if fbq is already initialized
    if (!window.fbq) {
      // Define the fbq function with proper queue initialization
      window.fbq = function(...args: unknown[]) {
        if (window.fbq.callMethod) {
          window.fbq.callMethod(...args);
        } else {
          if (!window.fbq.queue) {
            window.fbq.queue = [];
          }
          window.fbq.queue.push(args);
        }
      };
      
      // Set up Facebook Pixel global variables
      if (!window._fbq) {
        window._fbq = window.fbq;
      }
      
      // Set up Facebook Pixel properties
      window.fbq.push = window.fbq;
      window.fbq.loaded = true;
      window.fbq.version = '2.0';
      window.fbq.queue = [];
    }

    // Load the pixel script
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://connect.facebook.net/en_US/fbevents.js';
    document.head.appendChild(script);
    
    // Initialize with the pixel ID
    window.fbq('init', FACEBOOK_PIXEL_ID);
    
    // Default to requiring consent (revoked state)
    window.fbq('consent', 'revoke');
    
    // If consent was previously given, grant it
    const hasConsent = Cookies.get('fb_pixel_consent') === 'true';
    if (hasConsent) {
      window.fbq('consent', 'grant');
      window.fbq('track', 'PageView');
    }
  }
};

// Function to call when consent is granted
export const grantFacebookPixelConsent = () => {
  if (typeof window !== 'undefined') {
    // Initialize Facebook Pixel if not already initialized
    initFacebookPixel();
    
    // Set consent cookie
    Cookies.set('fb_pixel_consent', 'true', { expires: COOKIE_DURATION_DAYS });
    
    if (window.fbq) {
      // Grant consent to Facebook Pixel
      window.fbq('consent', 'grant');
      
      // Track initial page view
      window.fbq('track', 'PageView');
    }
  }
};

// Function to call when consent is revoked
export const revokeFacebookPixelConsent = () => {
  if (typeof window !== 'undefined' && window.fbq) {
    // Update consent cookie to false
    Cookies.set('fb_pixel_consent', 'false', { expires: COOKIE_DURATION_DAYS });
    
    // Revoke consent from Facebook Pixel
    window.fbq('consent', 'revoke');
    
    // Optional: Remove Facebook Pixel scripts and cookies for complete opt-out
    try {
      // Remove FB script tags
      const scripts = document.querySelectorAll('script[src*="facebook"]');
      scripts.forEach(script => script.parentNode?.removeChild(script));
      
      // Remove FB cookies (browser-specific)
      const fbCookies = ['_fbp', '_fbc'];
      fbCookies.forEach(name => {
        Cookies.remove(name);
        Cookies.remove(name, { path: '/' });
        // For top-level domains
        const domain = window.location.hostname;
        if (domain.includes('.')) {
          const topDomain = domain.substring(domain.indexOf('.'));
          Cookies.remove(name, { path: '/', domain: topDomain });
        }
      });
    } catch (error) {
      console.error('Error removing Facebook tracking:', error);
    }
  }
};

// Facebook Pixel component to initialize tracking
export default function FacebookPixel() {
  useEffect(() => {
    // Initialize the Facebook Pixel
    initFacebookPixel();
    
    // Track page views on route changes
    const handleRouteChange = () => {
      if (window.fbq && Cookies.get('fb_pixel_consent') === 'true') {
        window.fbq('track', 'PageView');
      }
    };

    // Listen for route changes in Next.js
    document.addEventListener('nextjs:route-change-complete', handleRouteChange);

    return () => {
      document.removeEventListener('nextjs:route-change-complete', handleRouteChange);
    };
  }, []);

  return null;
}

// Add TypeScript declaration for fbq
declare global {
  interface Window {
    fbq: {
      (...args: unknown[]): void;
      callMethod?: (...args: unknown[]) => void;
      queue?: unknown[];
      push?: unknown;
      loaded?: boolean;
      version?: string;
    };
    _fbq: unknown;
  }
}
