@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Studio Joshi Fonts - Using system and web fonts */
/* 
Typography System:
- Titles: NewYork (Apple system font) + fallbacks
- Subtitles: Nunito Sans (Google Fonts) - similar to Ranade
- Body: Nunito Sans Light (Google Fonts) - similar to Ranade Light
*/

:root {
  --foreground-rgb: 57, 36, 20; /* dark-brown */
  --background-start-rgb: 237, 230, 217; /* off-white */
  --background-end-rgb: 237, 230, 217; /* off-white */
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Nunito Sans', 'Inter', sans-serif;
  background-color: #EDE6D9; /* off-white */
  color: #392414; /* dark-brown */
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #EDE6D9;
}

::-webkit-scrollbar-thumb {
  background: #B2987C;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #775A41;
}

/* Smooth transitions for all elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}
