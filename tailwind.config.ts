import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Studio Joshi Brand Colors
        'dark-brown': '#392414',
        'light-brown': '#775A41', 
        'warm-beige': '#B2987C',
        'off-white': '#EDE6D9',
        
        // Legacy colors (keeping for compatibility)
        cream: {
          50: '#FEFCF8',
          100: '#FDF9F1',
          200: '#FAF0E0',
          300: '#F7E7CF',
          400: '#F1D5AD',
          500: '#EBC38B',
          600: '#D4A774',
          700: '#8F704E',
          800: '#6B543B',
          900: '#473828',
        },
        beige: {
          50: '#F8F6F3',
          100: '#F1EDE7',
          200: '#E3DBCF',
          300: '#D5C9B7',
          400: '#C7B79F',
          500: '#B9A587',
          600: '#948466',
          700: '#6F634D',
          800: '#4A4233',
          900: '#25211A',
        },
      },
      fontFamily: {
        // Studio Joshi Typography
        'title': ['NewYork', 'ui-serif', 'Georgia', 'serif'], // NewYork (system font on Apple devices)
        'subtitle': ['Nunito Sans', 'Inter', 'sans-serif'], // Similar to Ranade Regular
        'body': ['Nunito Sans', 'Inter', 'sans-serif'], // Similar to Ranade Light
        
        // Legacy
        sans: ['Inter', 'sans-serif'],
      },
      fontWeight: {
        'light': '300', // Ranade Light
        'regular': '400', // NewYork Regular, Ranade Regular
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}
export default config 