import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

// Facebook Pixel configuration from environment variables
const FACEBOOK_PIXEL_ID = process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID || '1077679850383928';
const FACEBOOK_ACCESS_TOKEN = process.env.FACEBOOK_ACCESS_TOKEN || 'EAARoXPjQnawBPE9TtgU6bMRaxm9FKYlatX0v3m1SqeRzQtVvkBHnmNfzawc2EQRQae8RssNGAO2ju6eic7r15xDsYfdJSvPBT1WALMRNhfw1uUKcyEdyagSwWRW9hbOe3z3H2OY3ZB1QeKfwZCRFxtZBz76t62Nb5VBQ5SWAJh4jiI8LxpWe9XWxePXXQZDZD';
const FACEBOOK_API_VERSION = process.env.FACEBOOK_API_VERSION || 'v18.0';
const FACEBOOK_ENDPOINT = `https://graph.facebook.com/${FACEBOOK_API_VERSION}/${FACEBOOK_PIXEL_ID}/events`;

// Rate limiting and retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// Hash function for PII data (required by Facebook)
function hashData(data: string): string {
  return crypto.createHash('sha256').update(data.toLowerCase().trim()).digest('hex');
}

// Get client IP address from request headers
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return request.ip || '';
}

// Validate event data
function validateEventData(data: any): boolean {
  if (!data.event_name || typeof data.event_name !== 'string') {
    return false;
  }
  
  if (!data.event_time || typeof data.event_time !== 'number') {
    return false;
  }
  
  if (!data.action_source || data.action_source !== 'website') {
    return false;
  }
  
  return true;
}

// Send event to Facebook Conversions API with retry logic
async function sendToFacebookAPI(eventData: any, retryCount = 0): Promise<any> {
  try {
    const response = await fetch(FACEBOOK_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${FACEBOOK_ACCESS_TOKEN}`,
      },
      body: JSON.stringify({
        data: [eventData],
        test_event_code: process.env.NODE_ENV === 'development' ? process.env.FACEBOOK_TEST_EVENT_CODE : undefined,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Facebook API error: ${response.status} - ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Facebook API request failed (attempt ${retryCount + 1}):`, error);
    
    // Retry logic for transient errors
    if (retryCount < MAX_RETRIES) {
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retryCount + 1)));
      return sendToFacebookAPI(eventData, retryCount + 1);
    }
    
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!validateEventData(body)) {
      return NextResponse.json(
        { error: 'Invalid event data' },
        { status: 400 }
      );
    }

    // Get client IP address
    const clientIP = getClientIP(request);
    
    // Prepare event data for Facebook Conversions API
    const eventData = {
      event_name: body.event_name,
      event_time: body.event_time,
      event_id: body.event_id,
      action_source: body.action_source,
      event_source_url: body.event_source_url,
      user_data: {
        client_ip_address: clientIP,
        client_user_agent: body.user_data?.client_user_agent || '',
        fbp: body.user_data?.fbp || undefined,
        fbc: body.user_data?.fbc || undefined,
      },
      custom_data: body.custom_data || {},
    };

    // Remove undefined values to clean up the payload
    Object.keys(eventData.user_data).forEach(key => {
      if (eventData.user_data[key] === undefined) {
        delete eventData.user_data[key];
      }
    });

    // Send to Facebook Conversions API
    const result = await sendToFacebookAPI(eventData);
    
    // Log successful events (without sensitive data)
    console.log(`Facebook event sent successfully: ${body.event_name}`, {
      event_id: body.event_id,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({ 
      success: true, 
      event_id: body.event_id,
      facebook_response: result 
    });

  } catch (error) {
    console.error('Facebook Conversions API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to send event to Facebook',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({ 
    status: 'ok',
    service: 'Facebook Conversions API',
    timestamp: new Date().toISOString()
  });
}
