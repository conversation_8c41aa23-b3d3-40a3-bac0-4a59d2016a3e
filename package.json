{"name": "frame-amsterdam", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/js-cookie": "^3.0.6", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "framer-motion": "^12.16.0", "js-cookie": "^3.0.5", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-cookie-consent": "^9.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8", "tailwindcss": "^3.4.0", "typescript": "^5"}}