'use client'

import { motion } from 'framer-motion'
import Script from 'next/script'
import Image from 'next/image'
import { Instagram } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'



// Instagram widget animation variants
const instagramVariants = {
  hidden: { 
    opacity: 0, 
    y: 40,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 1,
      ease: [0.25, 0.46, 0.45, 0.94],
      delay: 0.3
    }
  }
}

export default function AboutUs() {
  const { t } = useLanguage()

  return (
    <section 
      id="over-ons"
      className="py-12 sm:py-16 lg:py-20 bg-warm-beige/10 relative overflow-hidden"
    >
      {/* Load Instagram Widget Script */}
      <Script 
        src="https://cdn.lightwidget.com/widgets/lightwidget.js"
        strategy="lazyOnload"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Title Section */}
        <motion.div
          className="text-center mb-8 sm:mb-12 lg:mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            id="about-title"
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-title font-regular text-dark-brown mb-4"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {t('about.title')}
          </motion.h2>
          
          <motion.p
            className="text-base sm:text-lg font-body font-light text-light-brown max-w-3xl mx-auto leading-relaxed mb-6 sm:mb-8 px-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('about.subtitle')}
          </motion.p>
        </motion.div>

        {/* Main Story Section with Image */}
        <motion.div
          className="max-w-7xl mx-auto mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="bg-off-white/80 backdrop-blur-sm rounded-3xl p-6 sm:p-8 lg:p-12 shadow-lg">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
              {/* Image Section */}
              <motion.div
                className="order-2 lg:order-1"
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
              >
                <div className="relative aspect-[4/3] w-full rounded-2xl overflow-hidden shadow-lg">
                  <Image
                    src="/img/overons.jpeg"
                    alt="Studio Joshi - Over Ons - Anouk en Stephanie"
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-700"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 40vw"
                    priority
                  />
                </div>
              </motion.div>

              {/* Text Content */}
              <div className="order-1 lg:order-2 space-y-6">
                <motion.p
                  className="text-sm sm:text-base md:text-lg font-body font-light text-dark-brown leading-relaxed"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                  viewport={{ once: true }}
                >
                  {t('about.story1')}
                </motion.p>

                <motion.p
                  className="text-sm sm:text-base md:text-lg font-body font-light text-dark-brown leading-relaxed"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                  viewport={{ once: true }}
                >
                  {t('about.story2')}
                </motion.p>

                <motion.p
                  className="text-sm sm:text-base md:text-lg font-body font-light text-dark-brown leading-relaxed"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                  viewport={{ once: true }}
                >
                  {t('about.story3')}
                </motion.p>

                <motion.p
                  className="text-sm sm:text-base md:text-lg font-body font-light text-light-brown font-medium"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 1.0 }}
                  viewport={{ once: true }}
                >
                  {t('about.story4')}
                </motion.p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Services Section */}
        <section 
          id="services-widget" 
          className="py-12 sm:py-16 lg:py-20 bg-gradient-to-br from-light-brown/5 to-warm-beige/10 relative overflow-hidden mb-12 sm:mb-16"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <motion.div 
              className="space-y-6 sm:space-y-8"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <motion.h2 
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-title font-regular text-dark-brown leading-tight px-2"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                {t('services.title')}
              </motion.h2>
              
              <motion.div 
                className="max-w-6xl mx-auto bg-off-white/80 backdrop-blur-sm rounded-3xl p-4 sm:p-6 lg:p-8 shadow-lg overflow-hidden"
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <motion.div 
                  className="w-full"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  viewport={{ once: true }}
                >
                  <iframe 
                    src="https://studio-joshi.salonized.com/services?layout=embed" 
                    className="w-full h-[600px] border-none rounded-2xl bg-white shadow-inner" 
                    title="Studio Joshi - Diensten & Prijzen" 
                    loading="lazy"
                  />
                </motion.div>
              </motion.div>
              
              <motion.div 
                className="text-center pt-6"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
                viewport={{ once: true }}
              >
                <a 
                  href="#sz-booking-toggle" 
                  className="inline-flex items-center space-x-2 px-8 py-4 bg-gradient-to-r from-dark-brown to-light-brown text-off-white font-subtitle font-regular text-sm tracking-wide rounded-full hover:from-light-brown hover:to-warm-beige hover:text-dark-brown transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <span>{t('services.cta')}</span>
                </a>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Instagram Feed Section */}
        <motion.div
          className="max-w-5xl mx-auto mb-4 sm:mb-12 lg:mb-16"
          variants={instagramVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-50px" }}
        >
          <div className="text-center mb-8">
            <motion.div
              className="inline-flex items-center justify-center mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Instagram className="w-8 h-8 text-light-brown mr-3" />
                             <h3 className="text-2xl sm:text-3xl font-title font-regular text-dark-brown">
                 {t('instagram.title')}
               </h3>
            </motion.div>
                         <motion.div>
              {t('instagram.subtitle').trim() && t('instagram.subtitle') !== 'instagram.subtitle' && (
                <motion.p
                  className="text-base font-body font-light text-light-brown max-w-2xl mx-auto"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  {t('instagram.subtitle')}
                </motion.p>
              )}
            </motion.div>
          </div>
          
          <motion.div
            className="bg-off-white/60 backdrop-blur-sm rounded-3xl p-4 sm:p-6 lg:p-8 shadow-lg overflow-hidden"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            whileHover={{ 
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)",
              transition: { duration: 0.3 }
            }}
          >
            <iframe
              src="//lightwidget.com/widgets/2a1c548ef0925ef2912e92ba743a4b14.html"
              scrolling="no"
              {...{ allowtransparency: 'true' }}
              className="lightwidget-widget w-full border-0 overflow-hidden rounded-2xl"
              style={{ width: '100%', border: 0, overflow: 'hidden', minHeight: '400px', height: 'auto' }}
              title="Studio Joshi Instagram Feed"
            />
          </motion.div>
        </motion.div>


      </div>
    </section>
  )
} 