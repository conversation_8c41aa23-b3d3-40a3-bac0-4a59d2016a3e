'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'

type Language = 'NL' | 'EN'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const translations = {
  NL: {
    // Navigation
    'nav.home': 'HOME',
    'nav.about': 'OVER ONS',
    'nav.services': 'DIENSTEN',
    'nav.hours': 'OPENINGSTIJDEN',
    'nav.contact': 'CONTACT',
    
    // Hero Section
    'hero.welcome': '',
    'hero.studio': 'STUDIO JOSHI',
    'hero.subtitle': 'Welkom bij Studio Joshi - De specialist in hair extensions',
    'hero.description': 'Met meer dan 10 jaar ervaring werken wij met de mooiste technieken, om jouw haar op een veilige en natuurlijke manier te verlengen en/of verdikken. Voor knippen en kleuren (dames) kun je ook terecht bij ons.',
    'hero.cta': 'LEER ONS KENNEN',
    
    // About Section
    'about.title': 'Over Ons',
    'about.subtitle': 'Wij, <PERSON><PERSON><PERSON> en <PERSON>, zijn al ruim 10 jaar werkzaam in het kappersvak en hebben van onze passie ons werk gemaakt.',
    'about.story1': 'Na het afronden van onze kappersopleidingen deden we eerst ervaring op in een allround kapsalon, waar we de kneepjes van het vak leerden. Daarna werkten we zo\'n vier jaar in een salon die volledig gespecialiseerd was in hair extensions.',
    'about.story2': 'Daar groeide niet alleen onze liefde voor het vak, maar ook de droom om ooit zelf een salon te starten die draait om kwaliteit, service en het mooiste haar.',
    'about.story3': 'Naast het werken in de salon, heeft Stephanie ook enkele jaren als vertegenwoordiger voor een hair extensions merk gewerkt. Hierdoor kreeg zij een goed beeld van hoe verschillende salons met extensions werken.',
    'about.story4': 'Al die kennis, ervaring en gedeelde passie komen nu samen in onze eigen salon. Een plek waar we ons volledig richten op hoogwaardige hair extensions, met oog voor detail en een persoonlijke benadering.',
    'about.experience': '10+ Jaar',
    'about.experience.desc': 'Ervaring in het kappersvak',
    'about.specialization': 'Specialisatie',
    'about.specialization.desc': 'Hair extensions & kleur',
    'about.personal': 'Persoonlijk',
    'about.personal.desc': 'Maatwerk voor elke klant',
    'about.passion': 'Passie',
    'about.passion.desc': 'Liefde voor mooi haar',
    
    // Instagram Section
    'instagram.title': 'Volg ons op Instagram',
    'instagram.subtitle': '',
    
    // Services Section
    'services.title': 'Onze Diensten & Prijzen',
    'services.subtitle': '',
    'services.extensions.title': 'Hair Extensions',
    'services.extensions.desc': 'Professionele hair extensions met de mooiste technieken voor een natuurlijk resultaat',
    'services.extensions.feature1': 'Weft extensions',
    'services.extensions.feature2': 'Micro ring extensions',
    'services.extensions.feature3': 'Clip-in extensions',
    'services.extensions.feature4': 'Volume & lengte',
    'services.extensions.price': 'Prijzen op aanvraag',
    'services.cta': 'BOEK EEN AFSPRAAK',
    
    // Opening Hours
    'hours.title': 'Openingstijden',
    'hours.monday': 'Maandag',
    'hours.tuesday': 'Dinsdag',
    'hours.wednesday': 'Woensdag',
    'hours.thursday': 'Donderdag',
    'hours.friday': 'Vrijdag',
    'hours.saturday': 'Zaterdag',
    'hours.sunday': 'Zondag',
    'hours.closed': 'Gesloten',
    
    // Footer
    'footer.description': 'Hairextensions Expert in Hazerswoude Rijndijk. Met meer dan 10 jaar ervaring brengen wij jouw haar tot leven.',
    'footer.navigation': 'Navigatie',
    'footer.services': 'Onze Diensten',
    'footer.contact': 'Contact',
    'footer.hours': 'Openingstijden',
    'footer.services.extensions': 'Hair Extensions',
    'footer.services.cutting': 'Knippen (Dames)',
    'footer.services.coloring': 'Kleuren (Dames)',
    'footer.services.styling': 'Styling Advies',
    'footer.copyright': '© 2024 Studio Joshi. Alle rechten voorbehouden.',
    'footer.privacy': 'Privacy',
    'footer.terms': 'Voorwaarden',
    
    // Language
    'language.label': 'Taal: ',
    
    // Booking Widget
    'booking.title': 'Boek je Afspraak',
    'booking.description': 'Plan direct een afspraak in en laat ons jouw creatieve visie tot leven brengen.',
    // Booking Widget Features
    'booking.feature1.title': 'Flexibele Planning',
    'booking.feature1.desc': 'Kies een tijd die jou uitkomt',
    'booking.feature2.title': 'Snelle Bevestiging',
    'booking.feature2.desc': 'Direct bevestiging van je afspraak',
    'booking.feature3.title': 'Persoonlijke Benadering',
    'booking.feature3.desc': 'Op maat gemaakte design oplossingen',
    // Booking CTA
    'booking.cta.title': 'Klaar voor jouw transformatie?',
    'booking.cta.description': 'Plan nu je afspraak en laat ons jouw droomhaar werkelijkheid maken.',
    'booking.cta.button': 'BOEK NU',
  },
  EN: {
    // Navigation
    'nav.home': 'HOME',
    'nav.about': 'ABOUT US',
    'nav.services': 'SERVICES',
    'nav.hours': 'OPENING HOURS',
    'nav.contact': 'CONTACT',
    
    // Hero Section
    'hero.welcome': '',
    'hero.studio': 'STUDIO JOSHI',
    'hero.subtitle': 'Welcome to Studio Joshi - The specialist in hair extensions',
    'hero.description': 'With more than 10 years of experience, we work with the most beautiful techniques to extend and/or thicken your hair in a safe and natural way. You can also come to us for cutting and coloring (ladies).',
    'hero.cta': 'GET TO KNOW US',
    
    // About Section
    'about.title': 'About Us',
    'about.subtitle': 'We, Anouk and Stephanie, have been working in the hairdressing profession for over 10 years and have made our passion our work.',
    'about.story1': 'After completing our hairdressing training, we first gained experience in an all-round hair salon, where we learned the tricks of the trade. After that, we worked for about four years in a salon that was fully specialized in hair extensions.',
    'about.story2': 'There, not only did our love for the profession grow, but also the dream to one day start our own salon that revolves around quality, service and the most beautiful hair.',
    'about.story3': 'In addition to working in the salon, Stephanie also worked for several years as a representative for a hair extensions brand. This gave her a good insight into how different salons work with extensions.',
    'about.story4': 'All that knowledge, experience and shared passion now come together in our own salon. A place where we focus entirely on high-quality hair extensions, with attention to detail and a personal approach.',
    'about.experience': '10+ Years',
    'about.experience.desc': 'Experience in hairdressing',
    'about.specialization': 'Specialization',
    'about.specialization.desc': 'Hair extensions & color',
    'about.personal': 'Personal',
    'about.personal.desc': 'Custom work for every client',
    'about.passion': 'Passion',
    'about.passion.desc': 'Love for beautiful hair',
    
    // Instagram Section
    'instagram.title': 'Follow us on Instagram',
    'instagram.subtitle': '',
    
    // Services Section
    'services.title': 'Our Services & Prices',
    'services.subtitle': '',
    'services.extensions.title': 'Hair Extensions',
    'services.extensions.desc': 'Professional hair extensions with the most beautiful techniques for a natural result',
    'services.extensions.feature1': 'Weft extensions',
    'services.extensions.feature2': 'Micro ring extensions',
    'services.extensions.feature3': 'Clip-in extensions',
    'services.extensions.feature4': 'Volume & length',
    'services.extensions.price': 'Prices on request',
    'services.cta': 'BOOK AN APPOINTMENT',
    
    // Opening Hours
    'hours.title': 'Opening Hours',
    'hours.monday': 'Monday',
    'hours.tuesday': 'Tuesday',
    'hours.wednesday': 'Wednesday',
    'hours.thursday': 'Thursday',
    'hours.friday': 'Friday',
    'hours.saturday': 'Saturday',
    'hours.sunday': 'Sunday',
    'hours.closed': 'Closed',
    
    // Footer
    'footer.description': 'Hair Extensions Expert in Hazerswoude Rijndijk. With more than 10 years of experience, we bring your hair to life.',
    'footer.navigation': 'Navigation',
    'footer.services': 'Our Services',
    'footer.contact': 'Contact',
    'footer.hours': 'Opening Hours',
    'footer.services.extensions': 'Hair Extensions',
    'footer.services.cutting': 'Cutting (Ladies)',
    'footer.services.coloring': 'Coloring (Ladies)',
    'footer.services.styling': 'Styling Advice',
    'footer.copyright': '© 2024 Studio Joshi. All rights reserved.',
    'footer.privacy': 'Privacy',
    'footer.terms': 'Terms',
    
    // Language
    'language.label': 'Language: ',
    
    // Booking Widget
    'booking.title': 'Book your Appointment',
    'booking.description': 'Schedule your appointment directly and let us bring your creative vision to life.',
    // Booking Widget Features
    'booking.feature1.title': 'Flexible Scheduling',
    'booking.feature1.desc': 'Choose a time that suits you',
    'booking.feature2.title': 'Quick Confirmation',
    'booking.feature2.desc': 'Instant confirmation of your appointment',
    'booking.feature3.title': 'Personal Approach',
    'booking.feature3.desc': 'Tailor-made design solutions',
    // Booking CTA
    'booking.cta.title': 'Ready for your transformation?',
    'booking.cta.description': 'Schedule your appointment now and let us make your dream hair a reality.',
    'booking.cta.button': 'BOOK NOW',
  }
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('NL')

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
} 