'use client'

import { motion } from 'framer-motion'
import { Clock, MapPin, Mail } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'

export default function Services() {
  const { t } = useLanguage()
  
  const openingHours = [
    { day: t('hours.monday'), hours: '9:00 - 21:00' },
    { day: t('hours.tuesday'), hours: t('hours.closed') },
    { day: t('hours.wednesday'), hours: '9:00 - 18:00' },
    { day: t('hours.thursday'), hours: t('hours.closed') },
    { day: t('hours.friday'), hours: '9:00 - 18:00' },
    { day: t('hours.saturday'), hours: '9:00 - 17:00' },
    { day: t('hours.sunday'), hours: t('hours.closed') }
  ]

  return (
    <>
      {/* Opening Hours Section */}
      <section 
        id="openingstijden"
        className="py-12 sm:py-16 lg:py-20 bg-gradient-to-br from-warm-beige/20 to-light-brown/10 relative overflow-hidden"
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 60 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
            className="space-y-6 sm:space-y-8"
          >
            {/* Header */}
            <motion.h2
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-title font-regular text-dark-brown leading-tight px-2"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              {t('hours.title')}
            </motion.h2>

            {/* Opening Hours Grid */}
            <motion.div
              className="max-w-md mx-auto bg-off-white/80 backdrop-blur-sm rounded-3xl p-6 sm:p-8 shadow-lg"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <Clock className="w-12 h-12 text-light-brown mx-auto mb-6" />
              
              <div className="space-y-3">
                {openingHours.map((schedule, index) => (
                <motion.div 
                    key={schedule.day}
                    className="flex justify-between items-center py-2 border-b border-warm-beige/20 last:border-b-0"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                    viewport={{ once: true }}
                >
                    <span className="font-subtitle font-regular text-dark-brown">
                      {schedule.day}
                    </span>
                    <span className={`font-body font-light ${schedule.hours === 'Gesloten' ? 'text-light-brown/60' : 'text-light-brown'}`}>
                      {schedule.hours}
                    </span>
                </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
              <div className="flex items-center space-x-2 text-dark-brown">
                <MapPin className="w-5 h-5 text-light-brown" />
                <span className="font-body font-light">Potgieterlaan 76, Hazerswoude Rijndijk</span>
              </div>
              <div className="flex items-center space-x-2 text-dark-brown">
                <Mail className="w-5 h-5 text-light-brown" />
                <span className="font-body font-light"><EMAIL></span>
              </div>
        </motion.div>
      </motion.div>
        </div>
    </section>
    </>
  )
} 