'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { MapPin, Phone, Mail, Clock, Instagram } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'
import { openCookieSettings } from './CookieConsent'

export default function Footer() {
  const { t } = useLanguage()
  
  const navigation = [
    { name: t('nav.home'), href: '#home' },
    { name: t('nav.about'), href: '#over-ons' },
    { name: t('nav.services'), href: '#diensten' },
    { name: t('nav.hours'), href: '#openingstijden' },
    { name: t('nav.contact'), href: '#contact' },
  ]

  const services = [
    t('footer.services.extensions'),
    t('footer.services.cutting'),
    t('footer.services.coloring'),
    t('footer.services.styling')
  ]

  const openingHours = [
    `${t('hours.monday')}: 9:00 - 21:00`,
    `${t('hours.wednesday')}: 9:00 - 18:00`,
    `${t('hours.friday')}: 9:00 - 18:00`,
    `${t('hours.saturday')}: 9:00 - 17:00`
  ]

  return (
    <footer id="contact" className="bg-dark-brown text-off-white relative overflow-hidden">
      <div className="relative z-10">
        {/* Main Footer Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            
          {/* Company Info */}
          <motion.div
              className="space-y-4 lg:col-span-1"
              initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
              <Link href="/" className="inline-block">
              <Image
                src="/Logo/logo.png"
                alt="Studio Joshi"
                width={120}
                height={120}
                  className="w-auto h-16 filter brightness-0 invert"
                />
              </Link>
              <p className="text-warm-beige/80 font-body font-light text-sm leading-relaxed">
                {t('footer.description')}
              </p>

              {/* Social Links */}
            <div className="flex space-x-4">
                <motion.a
                  href="https://www.instagram.com/studio.joshi"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-warm-beige hover:text-off-white transition-colors duration-200"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Instagram className="w-5 h-5" />
                </motion.a>
            </div>
          </motion.div>

            {/* Navigation */}
          <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
              <h3 className="text-lg font-subtitle font-regular text-off-white">
                {t('footer.navigation')}
              </h3>
              <ul className="space-y-2">
                {navigation.map((item) => (
                  <li key={item.name}>
                  <Link
                      href={item.href}
                      className="text-warm-beige/80 hover:text-off-white font-body font-light text-sm transition-colors duration-200 block"
                  >
                      {item.name}
                  </Link>
                  </li>
              ))}
            </ul>
          </motion.div>

          {/* Services */}
          <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
              <h3 className="text-lg font-subtitle font-regular text-off-white">
                {t('footer.services')}
              </h3>
              <ul className="space-y-2">
                {services.map((service) => (
                  <li key={service}>
                    <span className="text-warm-beige/80 font-body font-light text-sm">
                    {service}
                  </span>
                  </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
              <h3 className="text-lg font-subtitle font-regular text-off-white">
                {t('footer.contact')}
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <MapPin className="w-4 h-4 text-light-brown mt-0.5 flex-shrink-0" />
                  <div className="text-warm-beige/80 font-body font-light text-sm">
                    Potgieterlaan 76<br />
                    2394VH Hazerswoude Rijndijk
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-light-brown flex-shrink-0" />
                <a 
                    href="tel:+31629117331"
                    className="text-warm-beige/80 hover:text-off-white font-body font-light text-sm transition-colors duration-200"
                >
                    +31 6 29 11 73 31
                </a>
                </div>

                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-light-brown flex-shrink-0" />
                <a 
                    href="mailto:<EMAIL>"
                    className="text-warm-beige/80 hover:text-off-white font-body font-light text-sm transition-colors duration-200"
                >
                    <EMAIL>
                </a>
                </div>
              </div>

              {/* Opening Hours Summary */}
              <div className="mt-6">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="w-4 h-4 text-light-brown" />
                  <span className="text-sm font-subtitle font-regular text-off-white">
                    {t('footer.hours')}
                  </span>
                </div>
                <div className="text-warm-beige/80 font-body font-light text-xs space-y-1">
                  {openingHours.map((hour) => (
                    <div key={hour}>{hour}</div>
                  ))}
                </div>
                </div>
              </motion.div>
            </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-warm-beige/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
              <motion.p
                className="text-warm-beige/60 font-body font-light text-sm text-center sm:text-left"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
        >
                {t('footer.copyright')}
              </motion.p>
              
              <motion.div
                className="flex space-x-6 text-sm"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                viewport={{ once: true }}
              >
              <Link 
                  href="#"
                  className="text-warm-beige/60 hover:text-off-white font-body font-light transition-colors duration-200"
              >
                  {t('footer.privacy')}
              </Link>
              <Link 
                  href="#"
                  className="text-warm-beige/60 hover:text-off-white font-body font-light transition-colors duration-200"
              >
                  {t('footer.terms')}
              </Link>
              <button
                onClick={openCookieSettings}
                className="text-warm-beige/60 hover:text-off-white font-body font-light transition-colors duration-200"
              >
                Cookie instellingen
              </button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}