'use client';

import { motion } from 'framer-motion';
import { Calendar, ArrowRight } from 'lucide-react';
import { useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { trackInitiateCheckout } from './FacebookPixel';

interface BookingWidgetProps {
  variant?: 'floating' | 'inline' | 'hero';
  className?: string;
}

export default function BookingWidget({ variant = 'inline', className = '' }: BookingWidgetProps) {
  const { t, language } = useLanguage();

  useEffect(() => {
    // Load Salonized script if not already loaded
    if (!document.querySelector('script[src="https://static-widget.salonized.com/loader.js"]')) {
      const script = document.createElement('script');
      script.src = 'https://static-widget.salonized.com/loader.js';
      script.async = true;
      document.head.appendChild(script);
    }
  }, []);

  // Handle booking widget click tracking
  const handleBookingClick = () => {
    // Track InitiateCheckout event when user clicks booking button
    trackInitiateCheckout();
  };

  if (variant === 'floating') {
    return (
      <motion.div
        className="fixed bottom-6 right-6 z-40"
        initial={{ opacity: 0, scale: 0.8, y: 100 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 1 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={handleBookingClick}
      >
        <div
          className="salonized-booking shadow-2xl rounded-full"
          data-company="WFXKawzFsztNxrm4yaa8Wf3y"
          data-color="#3b2518"
          data-language="nl"
          data-position="right"
          data-outline="shadow"
        />
      </motion.div>
    );
  }

  if (variant === 'hero') {
    return (
      <motion.div
        className={`w-full ${className}`}
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.5 }}
      >
        <motion.a
          href="#sz-booking-toggle"
          onClick={handleBookingClick}
          className="group w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-dark-brown to-light-brown text-off-white font-subtitle font-regular text-sm tracking-wide rounded-full hover:from-light-brown hover:to-warm-beige hover:text-dark-brown transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl"
          whileHover={{
            scale: 1.05,
            y: -5,
            boxShadow: "0 20px 40px rgba(57, 36, 20, 0.3)"
          }}
          whileTap={{ scale: 0.95 }}
        >
          <Calendar className="w-5 h-5" />
          <span>{t('services.cta')}</span>
          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
        </motion.a>
        
        {/* Hidden Salonized widget */}
        <div 
          className="salonized-booking hidden" 
          data-company="WFXKawzFsztNxrm4yaa8Wf3y" 
          data-color="#3b2518" 
          data-language={language.toLowerCase()} 
          data-height="700" 
          data-inline="true" 
          data-outline="shadow"
        />
      </motion.div>
    );
  }

  // Inline variant - full booking widget
  return (
    <motion.section
      className={`py-12 sm:py-16 lg:py-20 bg-off-white relative overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
    >
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-8 sm:mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <motion.h2
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-title font-regular text-dark-brown mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            {t('booking.title')}
          </motion.h2>
          <motion.p
            className="text-base sm:text-lg font-body font-light text-light-brown max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('booking.description')}
          </motion.p>
        </motion.div>

        {/* Booking Features */}
        {/* Amazing CTA Button */}
        <motion.div
          className="rounded-2xl overflow-hidden"
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.7 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-br from-dark-brown via-light-brown to-warm-beige p-8 sm:p-12 text-center relative overflow-hidden">
            {/* Content */}
            <div className="relative z-10">
              <motion.div
                className="inline-flex items-center justify-center w-16 h-16 bg-off-white/20 rounded-full mb-6"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <Calendar className="w-8 h-8 text-off-white" />
              </motion.div>
              
              <h3 className="text-2xl sm:text-3xl font-title font-regular text-off-white mb-4">
                {t('booking.cta.title')}
              </h3>
              
              <p className="text-base sm:text-lg font-body font-light text-off-white/90 mb-8 max-w-md mx-auto">
                {t('booking.cta.description')}
              </p>
              
              <motion.a
                href="#sz-booking-toggle"
                onClick={handleBookingClick}
                className="group inline-flex items-center justify-center px-8 py-4 bg-off-white text-dark-brown font-subtitle font-regular text-lg tracking-wide rounded-full hover:bg-warm-beige hover:text-off-white transition-all duration-300 shadow-lg hover:shadow-xl min-w-[200px]"
                whileHover={{
                  scale: 1.05,
                  y: -5,
                  boxShadow: "0 25px 50px rgba(0, 0, 0, 0.2)"
                }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="mr-3">{t('booking.cta.button')}</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </motion.a>
            </div>
          </div>
          
          {/* Hidden Salonized widget for inline booking */}
          <div 
            className="salonized-booking-inline hidden" 
            data-company="WFXKawzFsztNxrm4yaa8Wf3y" 
            data-color="#3b2518" 
            data-language={language.toLowerCase()} 
            data-height="700" 
            data-inline="true" 
            data-outline="shadow"
          />
        </motion.div>
      </div>
    </motion.section>
  );
} 