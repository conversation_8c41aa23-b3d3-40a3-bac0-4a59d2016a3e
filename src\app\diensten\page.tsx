import type { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Haar Services & Behandelingen | Studio Joshi Hazers<PERSON>ude',
  description: 'Ontdek alle haarservices van Studio Joshi: hairextensions, balayage, haarverlenging en knippen. Specialist in hair extensions en styling.',
  keywords: ['hairextensions', 'haarextensions', 'balayage', 'haarverlenging', 'haarsalon', 'Studio Joshi'],
  alternates: {
    canonical: 'https://studiojoshi.nl/diensten',
  }
}

export default function ServicesPage() {
  const services = [
    {
      id: 'hairextensions',
      title: 'Hairextensions',
      description: 'Professionele hairextensions voor langer en voller haar. Wij zijn specialist in diverse technieken voor een natuurlijke look.',
      image: '/diensten/hairextensions.png',
      link: '/diensten/hairextensions',
    },
    {
      id: 'balayage',
      title: 'Balayage',
      description: 'Natuurlijk ogende balayage haarkleuring met zachte overgangen voor een zongezoende look die mooi uitgroeit.',
      image: '/diensten/balayage.png',
      link: '/diensten/balayage',
    },
  ]

  return (
    <div className="container mx-auto px-4 py-12">
      <section className="mb-16">
        <h1 className="text-4xl md:text-5xl font-bold mb-6">Onze Diensten</h1>
        <p className="text-xl mb-12">
          Bij Studio Joshi zijn wij gespecialiseerd in premium haarextensions, professionele 
          haarkleuring en op maat gemaakt advies voor jouw perfecte look. Ontdek hieronder al onze diensten.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <div key={service.id} className="bg-white rounded-xl overflow-hidden shadow-lg transition-transform hover:scale-105">
              <div className="relative h-64">
                <Image
                  src={service.image}
                  alt={service.title}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-4">{service.description}</p>
                <Link 
                  href={service.link} 
                  className={`inline-block px-6 py-2 rounded-full ${
                    service.link === '#' 
                      ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
                      : 'bg-black text-white hover:bg-gray-800'
                  }`}
                >
                  {service.link === '#' ? 'Binnenkort beschikbaar' : 'Meer informatie'}
                </Link>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 p-8 bg-gray-50 rounded-xl">
          <h2 className="text-3xl font-bold mb-6">Op zoek naar een specifieke behandeling?</h2>
          <p className="text-lg mb-8">
            Staat jouw gewenste behandeling er niet tussen of wil je meer informatie over onze diensten?
            Neem gerust contact met ons op voor persoonlijk advies of maak direct een afspraak voor een 
            vrijblijvend consult.
          </p>
          <div className="flex flex-wrap gap-4">
            <Link href="/" className="px-8 py-3 bg-black text-white rounded-full hover:bg-gray-800">
              Terug naar home
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
