'use client';

import dynamic from 'next/dynamic';
import usePageTracking from '@/hooks/usePageTracking';

// Dynamically import client components
const FacebookPixel = dynamic(() => import('./FacebookPixel'), { ssr: false });
const CookieConsent = dynamic(() => import('./CookieConsent'), { ssr: false });

// Client component wrapper for cookie consent and tracking
export default function ClientComponents() {
  // Track page views automatically
  usePageTracking();

  return (
    <>
      <FacebookPixel />
      <CookieConsent />
    </>
  );
}
