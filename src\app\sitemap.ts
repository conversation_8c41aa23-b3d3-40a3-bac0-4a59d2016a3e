import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  // Base URL for the site
  const baseUrl = 'https://studiojoshi.nl'

  // Current date for lastModified
  const currentDate = new Date()

  // Main website pages
  const mainPages = [
    { route: '', priority: 1.0, changeFreq: 'weekly' },
    { route: '/about', priority: 0.8, changeFreq: 'monthly' },
    { route: '/contact', priority: 0.7, changeFreq: 'monthly' },
    { route: '/prijzen', priority: 0.8, changeFreq: 'monthly' },
    { route: '/diensten', priority: 0.9, changeFreq: 'weekly' },
    { route: '/gallery', priority: 0.7, changeFreq: 'monthly' },
    { route: '/blog', priority: 0.7, changeFreq: 'weekly' },
    { route: '/over-ons', priority: 0.6, changeFreq: 'monthly' },
  ].map(({ route, priority, changeFreq }) => ({
    url: `${baseUrl}${route}`,
    lastModified: currentDate,
    changeFrequency: changeFreq as 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never',
    priority,
  }))

  // Services pages - keeping only the services that are still offered
  const serviceRoutes = [
    { route: '/diensten', priority: 1.0 },
    { route: '/diensten/hairextensions', priority: 0.9 },
    { route: '/diensten/balayage', priority: 0.9 },
    { route: '/diensten/haarverlenging', priority: 0.8 },
    { route: '/diensten/weave-weft-extensions', priority: 0.8 },
  ]

  // Combine all routes
  return [...mainPages, ...serviceRoutes.map(({ route, priority }) => ({
    url: `${baseUrl}${route}`,
    lastModified: currentDate,
    changeFrequency: 'monthly' as const,
    priority,
  }))]
}
